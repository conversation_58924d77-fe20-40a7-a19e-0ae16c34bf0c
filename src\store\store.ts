import { create } from "zustand";

interface AuthStore {
    isAdmin: boolean;
    user: any; // Or define a more specific type for the user object
    setAdmin: (isAdmin: boolean) => void;
    setUser: (user: any) => void; // Or a specific user type
    logout: () => void; // New logout method to clear the store
}

export const useAuthStore = create<AuthStore>((set) => ({
    isAdmin: false,
    user: null, // Initially, no user is logged in
    setAdmin: (isAdmin: boolean) => set({ isAdmin }),
    setUser: (user: any) => set({ user }), // Update user details
    logout: () => set({ isAdmin: false, user: null }), // Reset the store values on logout
}));

// Cart Store Interfaces
export interface CartItem {
    id: number;
    name: string;
    description: string;
    price: number;
    image: string;
    category: string;
    quantity: number;
}

interface CartStore {
    items: CartItem[];
    addItem: (item: Omit<CartItem, 'quantity'>) => void;
    removeItem: (id: number) => void;
    updateQuantity: (id: number, quantity: number) => void;
    clearCart: () => void;
    getTotalItems: () => number;
    getTotalPrice: () => number;
}

export const useCartStore = create<CartStore>((set, get) => ({
    items: [],

    addItem: (item) => set((state) => {
        const existingItem = state.items.find(cartItem => cartItem.id === item.id);
        if (existingItem) {
            return {
                items: state.items.map(cartItem =>
                    cartItem.id === item.id
                        ? { ...cartItem, quantity: cartItem.quantity + 1 }
                        : cartItem
                )
            };
        } else {
            return {
                items: [...state.items, { ...item, quantity: 1 }]
            };
        }
    }),

    removeItem: (id) => set((state) => ({
        items: state.items.filter(item => item.id !== id)
    })),

    updateQuantity: (id, quantity) => set((state) => {
        if (quantity <= 0) {
            return {
                items: state.items.filter(item => item.id !== id)
            };
        }
        return {
            items: state.items.map(item =>
                item.id === id ? { ...item, quantity } : item
            )
        };
    }),

    clearCart: () => set({ items: [] }),

    getTotalItems: () => {
        const state = get();
        return state.items.reduce((total, item) => total + item.quantity, 0);
    },

    getTotalPrice: () => {
        const state = get();
        return state.items.reduce((total, item) => total + (item.price * item.quantity), 0);
    }
}));
